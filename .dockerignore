# Git
.git
.github
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build directories
dist
build
coverage

# Environment files
.env
.env.*
!.env.example

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Logs
logs
*.log

# Tests
/test
/coverage

# Temporary files
/tmp
/temp 