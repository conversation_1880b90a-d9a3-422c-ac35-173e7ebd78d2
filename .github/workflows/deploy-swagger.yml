name: Deploy Swagger API Documentation

on:
  push:
    branches:
      - main
    paths:
      - 'src/**'
      - 'openapi/**'
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      # App Environment
      NODE_ENV: ${{ vars.NODE_ENV || 'development' }}
      API_DOC_VERSION: ${{ vars.API_DOC_VERSION || '1.0.0' }}
      PORT: ${{ vars.PORT || '7009' }}
      HOST: ${{ vars.HOST || 'localhost' }}
      API_PREFIX: ${{ vars.API_PREFIX || 'api' }}
      APP_NAME: ${{ vars.APP_NAME || 'NestJS Project Template' }}
      APP_DESCRIPTION: ${{ vars.APP_DESCRIPTION || 'NestJS template with best practices' }}
      API_VERSION: ${{ vars.API_VERSION || '1.0' }}
      OPEN_API_VERSION: ${{ vars.OPEN_API_VERSION || '3.1.0' }}
      
      # Database
      DATABASE_URL: ${{ secrets.DATABASE_URL || 'mongodb://localhost:27017/nestjs_practice' }}
      
      # Auth
      ALLOWED_ORIGINS: ${{ vars.ALLOWED_ORIGINS || 'localhost:*,127.0.0.1,localhost:7009,*:7009' }}
      JWT_SECRET: ${{ secrets.JWT_SECRET || 'dummy-jwt-secret-for-swagger-generation' }}
      JWT_EXPIRES_IN: ${{ vars.JWT_EXPIRES_IN || '30d' }}
      JWT_REFRESH_SECRET: ${{ secrets.JWT_REFRESH_SECRET || 'dummy-jwt-refresh-secret-for-swagger-generation' }}
      JWT_REFRESH_EXPIRES_IN: ${{ vars.JWT_REFRESH_EXPIRES_IN || '90d' }}
      
      # Storage
      STORAGE_PROVIDER: ${{ vars.STORAGE_PROVIDER || 's3' }}
      STORAGE_BUCKET: ${{ vars.STORAGE_BUCKET || 'my-bucket' }}
      STORAGE_REGION: ${{ vars.STORAGE_REGION || 'us-east-1' }}
      STORAGE_ENDPOINT: ${{ vars.STORAGE_ENDPOINT || 's3.us-east-1.amazonaws.com' }}
      STORAGE_ACCESS_KEY_ID: ${{ secrets.STORAGE_ACCESS_KEY_ID || 'dummy-access-key-id' }}
      STORAGE_ACCESS_KEY_SECRET: ${{ secrets.STORAGE_ACCESS_KEY_SECRET || 'dummy-access-key-secret' }}
      STORAGE_BASE_URL: ${{ vars.STORAGE_BASE_URL || 'https://my-bucket.s3.us-east-1.amazonaws.com' }}
      STORAGE_MAX_FILE_SIZE: ${{ vars.STORAGE_MAX_FILE_SIZE || '10485760' }}
      STORAGE_ALLOWED_MIME_TYPES: ${{ vars.STORAGE_ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/gif,application/pdf' }}
      
      # Datadog
      DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY || 'dummy-datadog-api-key' }}
      DATADOG_SERVICE_NAME: ${{ vars.DATADOG_SERVICE_NAME || 'nestjs-structure-practices' }}
      DATADOG_HOST_NAME: ${{ vars.DATADOG_HOST_NAME || 'local-dev' }}
      DATADOG_INTAKE_REGION: ${{ vars.DATADOG_INTAKE_REGION || 'us5' }}
      
      # Stripe
      STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY || 'sk_test_dummy_secret_key' }}
      STRIPE_PUBLIC_KEY: ${{ secrets.STRIPE_PUBLIC_KEY || 'pk_test_dummy_public_key' }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET || 'whsec_dummy_webhook_secret' }}
      STRIPE_API_VERSION: ${{ vars.STRIPE_API_VERSION || '2025-02-24.acacia' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
          
      - name: Install dependencies
        run: pnpm install

      - name: Generate Prisma client
        run: pnpm prisma:generate
        
      - name: Generate OpenAPI documentation
        run: |
          # Start the server in the background
          pnpm start:dev &
          
          # Wait for server to be ready (adjust sleep time if needed)
          sleep 15
          
          # Generate OpenAPI documentation
          pnpm openapi:export
          
          # Stop the background server process
          kill $(jobs -p)
          
          # Create directory for static files
          mkdir -p swagger-static
          
          # Move the generated OpenAPI JSON file to the static directory
          cp openapi/openapi.yaml swagger-static/swagger.yaml
          
          cat > swagger-static/index.html << 'EOF'
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <title>NestJS API Documentation</title>
            <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5/swagger-ui.css">
            <style>
              html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
              *, *:before, *:after { box-sizing: inherit; }
              body { margin: 0; background: #fafafa; }
              .topbar { display: none; }
              .swagger-ui .info { margin: 30px 0; }
              .swagger-ui .info .title { font-size: 32px; }
              .swagger-ui .info .description { font-size: 16px; line-height: 1.5; }
            </style>
          </head>
          <body>
            <div id="swagger-ui"></div>
            <script src="https://unpkg.com/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
            <script>
              window.onload = function() {
                window.ui = SwaggerUIBundle({
                  url: "./swagger.yaml",
                  dom_id: '#swagger-ui',
                  deepLinking: true,
                  presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIBundle.SwaggerUIStandalonePreset
                  ],
                  layout: "BaseLayout",
                  supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                });
              };
            </script>
          </body>
          </html>
          EOF

      - name: Deploy to GitHub Pages
        id: deployment
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./swagger-static
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
          commit_message: 'chore: update API documentation' 