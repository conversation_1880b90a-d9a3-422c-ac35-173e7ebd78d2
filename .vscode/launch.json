{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--debug", "--watch"], "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "--require", "dotenv/config"], "sourceMaps": true, "cwd": "${workspaceFolder}", "console": "integratedTerminal", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "env": {"NODE_ENV": "development"}}, {"type": "node", "request": "launch", "name": "Debug Jest Tests", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--config", "jest.config.ts"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true}, {"type": "node", "request": "launch", "name": "Debug E2E Tests", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--config", "test/jest-e2e.json"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true}]}