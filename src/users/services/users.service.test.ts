import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { UsersService } from './users.service';
import { PrismaService } from '@app/prisma/prisma.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { CreateUserDto } from '../dto/create-user.dto';
import { Role } from '@prisma/client';

// Mock bcrypt
void mock.module('bcryptjs', () => ({
  hash: mock(() => Promise.resolve('hashed-password')),
}));

describe('UsersService', () => {
  let service: UsersService;
  let prismaService: PrismaService;

  // Mock Prisma client
  const mockPrismaService = {
    user: {
      findMany: mock(),
      findUnique: mock(),
      create: mock(),
    },
  };

  beforeEach(() => {
    prismaService = mockPrismaService as any;
    service = new UsersService(prismaService);

    // Reset mocks
    mockPrismaService.user.findMany.mockReset();
    mockPrismaService.user.findUnique.mockReset();
    mockPrismaService.user.create.mockReset();
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      const expectedUsers = [
        {
          id: '1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          role: Role.USER,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.user.findMany.mockResolvedValue(expectedUsers);

      const result = await service.findAll();

      expect(result).toEqual(expectedUsers);
      expect(mockPrismaService.user.findMany).toHaveBeenCalledWith({
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      const expectedUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: Role.USER,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(expectedUser);

      const result = await service.findOne('1');

      expect(result).toEqual(expectedUser);
      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.findOne('999')).rejects.toThrow(NotFoundException);
      await expect(service.findOne('999')).rejects.toThrow('User with ID 999 not found');
    });
  });

  describe('findByEmail', () => {
    it('should return a user by email', async () => {
      const expectedUser = {
        id: '1',
        email: '<EMAIL>',
        password: 'hashed-password',
        firstName: 'Test',
        lastName: 'User',
        role: Role.USER,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(expectedUser);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toEqual(expectedUser);
      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.findByEmail('<EMAIL>')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('create', () => {
    const createUserDto: CreateUserDto = {
      email: '<EMAIL>',
      password: 'plaintext-password',
      firstName: 'New',
      lastName: 'User',
    };

    it('should create a new user successfully', async () => {
      const expectedUser = {
        id: '2',
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: Role.USER,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock user doesn't exist
      mockPrismaService.user.findUnique.mockResolvedValue(null);
      // Mock successful creation
      mockPrismaService.user.create.mockResolvedValue(expectedUser);

      const result = await service.create(createUserDto);

      expect(result).toEqual(expectedUser);
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          password: 'hashed-password',
          firstName: 'New',
          lastName: 'User',
          role: Role.USER,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });
    });

    it('should throw ConflictException when user already exists', async () => {
      const existingUser = {
        id: '1',
        email: '<EMAIL>',
        password: 'existing-password',
        firstName: 'Existing',
        lastName: 'User',
        role: Role.USER,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(existingUser);

      await expect(service.create(createUserDto)).rejects.toThrow(ConflictException);
      await expect(service.create(createUserDto)).rejects.toThrow(
        'User <NAME_EMAIL> already exists',
      );
    });
  });
});
