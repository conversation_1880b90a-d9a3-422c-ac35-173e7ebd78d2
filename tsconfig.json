{"include": ["src/**/*"], "compilerOptions": {"module": "ES2022", "moduleResolution": "node", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "lib": ["ES2022"], "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "types": ["bun-types"], "paths": {"@app/*": ["src/*"], "@config/*": ["src/config/*"], "@common/*": ["src/common/*"], "@shared/*": ["src/shared/*"], "@core/*": ["src/core/*"]}}, "exclude": ["node_modules", "dist", "test", "**/*.spec.ts", "prisma/**/*"]}